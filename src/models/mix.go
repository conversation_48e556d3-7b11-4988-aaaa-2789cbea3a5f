package models

import (
	"roadtrip-api/src/constmap"

	"golang.org/x/crypto/bcrypt"
)

func migrate() error {
	var tables = []any{
		&Wish{},
		&WishExt{},
		&WishTodo{},
		&WishTag{},
		&WishMember{},
		&WishMedia{},
		&Plan{},
		&Like{},
		&LikeDo{},
	}

	if err := db.Set("gorm:table_options", "Engine=innodb DEFAULT CHARSET=utf8mb4").
		AutoMigrate(tables...); err != nil {
		return err
	}

	var c int64
	if db.Model(&AdminUser{}).Count(&c); c == 0 {
		password, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
		db.Create(&AdminUser{
			Username: "admin",
			Nickname: "管理员",
			Mobile:   "15800449389",
			Password: string(password),
		})
	}
	if db.Model(&ScenicCate{}).Count(&c); c == 0 {
		db.Create([]*ScenicCate{
			{Value: "风景名胜"},
			{Value: "寺庙道馆"},
			{Value: "世界遗产"},
			{Value: "动物园"},
			{Value: "植物园"},
			{Value: "主题乐园"},
			{Value: "纪念馆"},
			{Value: "红色景区"},
			{Value: "海滩"},
		})
	}

	if db.Model(&TuanCate{}).Count(&c); c == 0 {
		db.Create([]TuanCate{
			{Name: "亲子"},
			{Name: "研学"},
			{Name: "私家团"},
			{Name: "拼团行"},
			{Name: "海岛"},
		})
	}

	if db.Model(&VipConf{}).Count(&c); c == 0 {
		conf := VipConf{
			Name:        "义伴黄金会员",
			Desc:        "专属权益全面升级,价值998元/年",
			MarketPrice: 99800,
			SalePrice:   36500,
			Years:       1,
			Icon:        "",
			State:       constmap.Enable,
		}
		db.Create(&conf)
		if db.Model(&VipBenefit{}).Count(&c); c == 0 {
			db.Create([]VipBenefit{
				{
					VipConfId:   conf.ID,
					Code:        constmap.VipBenefitTravel,
					Name:        "目的地旅游",
					Desc:        "全平台团游产品",
					BenefitType: constmap.VipBenefitTypeDiscount,
					Discount:    0.88,
					Icon:        "",
				},
				{
					VipConfId:   conf.ID,
					Code:        constmap.VipBenefitWifi,
					Name:        "机上WIFI无限流量包",
					Desc:        "覆盖东航、吉祥全部宽体机",
					BenefitType: constmap.VipBenefitTypeTimes,
					Times:       4,
					Icon:        "",
				},
				{
					VipConfId:   conf.ID,
					Code:        constmap.VipBenefitPickUp,
					Name:        "机场接送机服务",
					Desc:        "全国所有客运机场",
					BenefitType: constmap.VipBenefitTypeTimes,
					Times:       4,
					Icon:        "",
				},
				{
					VipConfId:   conf.ID,
					Code:        constmap.VipBenefitRentCar,
					Name:        "租车服务",
					Desc:        "全国所有城市",
					BenefitType: constmap.VipBenefitTypeDiscount,
					Discount:    0.95,
					Icon:        "",
				},
				{
					VipConfId:   conf.ID,
					Code:        constmap.VipBenefitAct,
					Name:        "线下自驾主题活动",
					Desc:        "每季度举行自驾活动",
					BenefitType: constmap.VipBenefitTypeDiscount,
					Discount:    0.55,
					Icon:        "",
				},
			})
		}
	}

	if db.Model(&User{}).Where("id=?", constmap.RobotUserId).Count(&c); c == 0 {
		u := &User{
			Nickname: "义伴AI",
		}
		u.ID = constmap.RobotUserId
		db.Create(u)
	}

	return nil
}
