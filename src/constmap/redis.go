package constmap

// RK kv
// RS Set
// RH Hash
// RL List
// RP Pub/Sub
// RZ Sorted Set(ZSet)
const (
	RKMutex              = "mx:%s:%s"           // 互斥锁 {主题}:{主键}
	RKAiResp             = "ai:resp:%s"         // AI响应 {reqId}
	RKAiCtxId            = "ai:ctxId:%d"        // 上下文ID {userId}
	RKSpin               = "spin:%s:%s"         //自旋等待锁 {主题}:{主键}
	RKZones              = "zones"              //所有地区
	RKWayMap             = "waymap:%s:%d"       //路线规划缓存 {aiReqId}、{planId}
	RKHoliday            = "holiday:%d"         //节假日缓存 {年份:2024}
	RKGeo                = "geo:%s:%s"          //geo接口缓存 {lng}{lat}
	RKCtxChatList        = "chatList:%d"        //会话列表 {userId}
	RLCtxChat            = "chat:%d:%s"         //单个会话 {userId}:{contextId}
	RKSpider             = "spider:%s:%d"       //爬虫缓存 {type:scene} {zoneId}
	RLRobotPlan          = "robotPlan:%d%s"     //推荐行程 {zoneId} {hotTag}
	RKPlanHotTags        = "planHotTags:%d"     //城市行程推荐热门标签 {zoneId}
	RKRecPrompts         = "recPrompts:%d"      //推荐提示词 {zoneId}
	RKSysConfig          = "sysConfig:%s"       //系统配置 {key}
	RSHotelKnowledgeSync = "hotelKnowledgeSync" //同步知识库
	RKZoneSpeciality     = "zoneSpeciality:%d"  //城市美食、特产、民俗文化 {zoneId}
	RKAdView             = "adView:%s"          //看广告得积分校验 {randStr}
	RKWishTop            = "wishTop:%s"         //心愿单热门信息 {type:zone|tag|holiday}
	RKHotZones           = "hotZones"           //热门城市
)

const (
	EventQueueDelay  = appCode + ":delay"
	EventQueueLight  = appCode + ":light"
	EventQueueWeight = appCode + ":weight" //比较耗时的队列
	EventQueuePdf    = appCode + ":pdf"    // PDF生成
)

const (
	EventScenicUpdate   = iota + 1 //事件类型（景点更新）
	EventTuanUpdate                //事件类型（团游更新）
	EventOrderPay                  //事件类型（请求发起第三方支付）
	EventMail                      //事件类型（发送邮件）
	EventTaskAction                //事件类型（任务行为）
	EventSubMsg                    //事件类型（订阅消息）
	EventAirCodeReward             //事件类型（兑换机上wifi）
	EventPlanPdf                   //事件类型（生成PDF）
	EventHotelAvgPrice             //事件类型（酒店价格同步）
	EventPlanUpdated               //事件类型（行程内容变动）
	EventWishSubmit                //事件类型（提交心愿单）
	EventWishSyncEs                //事件类型（同步心愿单到ES）
	EventWishJoinMember            //事件类型（心愿单加入同行人）
	EventWishSuccess               //事件类型（心愿单达成）
	EventWishClose                 //事件类型（心愿单关闭）
)
