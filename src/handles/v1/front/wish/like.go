package wish

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Like(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	// 防并发处理 - 防止同一用户对同一心愿单的并发操作
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishLike", fmt.Sprintf("%d:%d", session.UserId, in.WishId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur5s)
	if err != nil {
		my_logger.Infof("WishLike Mutex", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()

	// 检查心愿单是否存在
	var wish = new(models.Wish)
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	var out struct {
		Op    bool `json:"op"`    // true=点赞, false=取消点赞
		Likes int  `json:"likes"` // 最新点赞总数
	}

	// 使用新的通用点赞系统
	isLike, likes, err := like_biz.DoLike(db, constmap.LikeTypeWish, in.WishId, session.UserId)
	if err != nil {
		my_logger.Errorf("WishLike", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	out.Op = isLike
	out.Likes = likes

	// 同步更新心愿单表中的点赞数（保持数据一致性）
	err = db.Model(&models.Wish{}).Where("id=?", in.WishId).Update("likes", likes).Error

	if err != nil {
		my_logger.Errorf("WishLike sync to wish table", zap.Error(err))
		// 同步失败不影响点赞操作的成功，只记录错误
	}

	// 异步同步到ES
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": convertor.ToString(in.WishId),
	})

	return out, nil
}
