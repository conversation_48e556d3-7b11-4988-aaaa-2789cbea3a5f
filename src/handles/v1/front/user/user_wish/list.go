package user_wish

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
)

// 我的心愿单
func List(ctx *gin.Context) (any, error) {
	type tab string
	const (
		TabAll   tab = "all"
		TabWant  tab = "want"
		TabReach tab = "reach"
	)

	var in struct {
		Tab string `form:"tab"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if strutil.IsBlank(in.Tab) {
		in.Tab = string(TabAll)
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	// 构建查询条件
	query := db.Where("user_id = ?", session.UserId)

	// 根据tab参数过滤状态
	switch in.Tab {
	case string(TabWant):
		query = query.Where("state IN (?)", []constmap.WishState{constmap.WishStateWaitReview, constmap.WishStateProcessing})
	case string(TabReach):
		query = query.Where("state IN (?)", []constmap.WishState{constmap.WishStateSuccess, constmap.WishStateFinished})
	}

	// 查询总数和列表数据
	var total int64
	query.Model(&models.Wish{}).Count(&total)

	var wishes []models.Wish
	query.Preload("Members").Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&wishes)

	type vitem struct {
		WishId        uint     `json:"wish_id"`
		Title         string   `json:"title"`
		Cover         string   `json:"cover"`
		From          string   `json:"from"`
		To            string   `json:"to"`
		DepartDate    string   `json:"depart_date"`
		ReturnDate    string   `json:"return_date"`
		DateStr       string   `json:"date_str"`
		TotalPeople   int      `json:"total_people"`
		CurrentPeople int      `json:"current_people"`
		WishDesc      string   `json:"wish_desc"`
		Tags          []string `json:"tags"`
		StateText     string   `json:"state_text"`
		Likes         int64    `json:"likes"`
		IsLike        bool     `json:"is_like"`
		CreatedAt     int64    `json:"created_at"`
		OpenScope     int      `json:"open_scope"`
		CanClose      bool     `json:"can_close"`
	}

	// 批量查询标签，避免N+1问题
	tagIdSet := typeset.NewTypeSet[uint](false)
	wishIds := make([]uint, 0)
	for _, wish := range wishes {
		tagIdSet.Add(*wish.TagIds.Data...)
	}

	tagMap := wish_biz.LoadWishTags(db, tagIdSet.Values())

	list := make([]vitem, 0, len(wishes))

	likeMap := like_biz.LoadLikes(db, constmap.LikeWish, wishIds)
	likeDoMap := like_biz.LoadLikesDo(db, constmap.LikeWish, wishIds, session.UserId)

	// 组装返回数据
	for _, wish := range wishes {
		// 解析标签
		tags := []string{}
		for _, tagId := range *wish.TagIds.Data {
			if tag, exists := tagMap[tagId]; exists {
				tags = append(tags, tag.Tag)
			}
		}

		// 计算当前人数
		currentPeople := 0
		for _, member := range wish.Members {
			if member.State == constmap.WishMemberStateApproved {
				currentPeople++
			}
		}

		list = append(list, vitem{
			WishId:        wish.ID,
			Title:         wish.Title,
			Cover:         utils.StaticUrl(wish.Cover),
			From:          wish.From,
			To:            wish.To,
			DepartDate:    wish.DepartDate,
			ReturnDate:    wish.ReturnDate,
			DateStr:       wish_biz.GetDepartReturnStr(wish.DepartDate, wish.ReturnDate),
			TotalPeople:   wish.TotalPeople,
			CurrentPeople: currentPeople,
			WishDesc:      wish.WishDesc,
			Tags:          tags,
			StateText:     business.WishStateFrontText(wish.State),
			Likes:         likeMap[wish.ID],
			IsLike:        likeDoMap[wish.ID],
			CreatedAt:     wish.CreatedAt.Unix(),
			OpenScope:     wish.OpenScope,
			CanClose:      wish.State == constmap.WishStateWaitReview || wish.State == constmap.WishStateProcessing,
		})
	}

	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}
	out.Total = total
	out.List = list
	return out, nil
}
