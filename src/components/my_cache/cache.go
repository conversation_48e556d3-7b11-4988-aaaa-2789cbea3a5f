package my_cache

import (
	"context"
	"encoding"
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/go-redis/redis"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"sync"
	"time"
)

var c *redis.Client
var once sync.Once

func initRedis() {
	c = redis.NewClient(&redis.Options{
		Addr:     config.Config.Redis.Host,
		DB:       config.Config.Redis.Db,
		PoolSize: 200,
	})
}

func RedisClient() *redis.Client {
	once.Do(initRedis)
	return c
}

func Lock(key string, expire time.Duration) bool {
	if cmd := RedisClient().SetNX(key, byte(1), expire); !cmd.Val() || cmd.Err() != nil {
		return false
	}

	return true
}

func Mutex(key string, expire time.Duration) (func(), error) {
	if cmd := RedisClient().SetNX(key, byte(1), expire); !cmd.Val() || cmd.Err() != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgConcurrency)
	}
	return func() {
		RedisClient().Del(key)
	}, nil
}

func MGet(keys ...string) bool {
	cmd := RedisClient().MGet(keys...)
	if cmd.Err() != nil {

	}
	values, err := cmd.Result()
	if err != nil {
		return false
	}

	fmt.Println(values)

	return true
}

func Get(key string, any2 any) bool {
	cmd := RedisClient().Get(key)
	if cmd.Err() != nil {
		return false
	}
	if err := cmd.Scan(any2); err != nil {
		return false
	}

	return true
}

func SetDefault(key string, val any) error {
	return Set(key, val, constmap.DefaultCacheTime)
}

func Set(key string, val any, expire time.Duration) error {
	status := RedisClient().Set(key, val, expire)

	return status.Err()
}

func Remove(key string) {
	RedisClient().Del(key)
}

func Exists(keys ...string) int64 {
	return RedisClient().Exists(keys...).Val()
}

func RedisLock(ctx context.Context, key string, expire time.Duration) {
	ticker := time.NewTicker(time.Millisecond * 100)
	defer func() {
		ticker.Stop()
	}()

	for {
		if ok := Lock(key, expire); ok {
			break
		}

		select {
		case <-ticker.C:
			ticker.Reset(time.Millisecond * 100)
		}
	}

	go func() {
		select {
		case <-ctx.Done():
			Remove(key)
		}
	}()
}

// 批量查询key，未查询到的key使用回调方法查询后放入缓存
func MGetSet[K any, V any](
	keys map[string]K,
	callback func(missKeys map[string]K) (map[string]*V, error),
	expire time.Duration,
) (map[string]*V, error) {

	ret := make(map[string]*V)
	if len(keys) == 0 {
		return ret, nil
	}
	ckeys := maputil.Keys(keys)
	rds := RedisClient()
	cmd := rds.MGet(ckeys...)
	if cmd.Err() != nil && !errors.Is(cmd.Err(), redis.Nil) {
		return ret, cmd.Err()
	}
	missKeys := make(map[string]K)
	for i, v := range cmd.Val() {
		curKey := ckeys[i]
		var vv = new(V)
		if v == nil || v.(string) == "" {
			missKeys[curKey] = keys[curKey]
			continue
		} else if unmarshaler, ok := any(vv).(encoding.BinaryUnmarshaler); !ok {
			missKeys[curKey] = keys[curKey]
			continue
		} else {
			_ = unmarshaler.UnmarshalBinary([]byte(v.(string)))
			ret[curKey] = vv
		}
	}
	if len(missKeys) > 0 {
		if val, err := callback(missKeys); err != nil {
			return ret, err
		} else {
			if _, err = rds.Pipelined(func(pipeliner redis.Pipeliner) error {
				for k, v := range val {
					ret[k] = v
					pipeliner.Set(k, v, expire)
				}
				return nil
			}); err != nil {
				return ret, err
			}
		}
	}
	return ret, nil
}
