package cron

import (
	"encoding/json"
	"errors"
	redisstream "gitee.com/yjsoft-sh/redis-stream/src"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/hotel_biz/hotel_asm"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/business/qiyeweixin_biz"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/business/tuan_biz"
	"roadtrip-api/src/components/business/wechat_biz"
	"roadtrip-api/src/components/business/wish_biz/wish_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func startMq() {

	go normal()
	go delay()
	go pdf()
	go weight()
}

func weight() {
	consumer := my_queue.Client().CreateConsumerGroup(constmap.EventQueueWeight, &redisstream.ConsumerOption{})
	consumer.Callback(func(message *redisstream.Payload) error {
		buf, _ := json.Marshal(message.Payload)
		var payload my_queue.Payload
		if err := json.Unmarshal(buf, &payload); err != nil {
			return nil
		}

		my_logger.Infof("get weight queue message", zap.String("id", message.ID), zap.Int("event", payload.Event))

		switch payload.Event {
		case constmap.EventPlanUpdated:
			var id int64
			var force bool
			var aiReqid string
			if v, ok := payload.Payload["plan_id"]; ok {
				id, _ = convertor.ToInt(v)
			}
			if v, ok := payload.Payload["force"]; ok {
				force, _ = v.(bool)
			}
			if v, ok := payload.Payload["ai_reqid"]; ok {
				aiReqid, _ = v.(string)
			}
			planId := uint(id)
			if err := plan_asm.EventPlanUpdated(models.New(), planId, aiReqid, force); err != nil {
				my_logger.Errorf("EventPlanUpdated", zap.Uint("planId", planId), zap.String("aiReqid", aiReqid), zap.Error(err))
			} else {
				my_logger.Infof("EventPlanUpdated", zap.Uint("planId", planId), zap.String("aiReqid", aiReqid))
			}
		case constmap.EventWishSubmit:
			if v, ok := payload.Payload["id"]; ok {
				id, _ := convertor.ToInt(v)
				if err := wish_asm.RiskCheck(models.New(), uint(id)); err != nil {
					my_logger.Errorf("wish submit risk check error", zap.Int64("id", id), zap.Error(err))
					var appError constmap.AppError
					if errors.As(err, &appError) {
						return appError
					}
				} else {
					my_logger.Infof("wish submit risk check success", zap.Int64("id", id))
				}
			}
		}
		return nil
	})

	_ = consumer.Listen()
}

// 异步队列
func normal() {
	consumer := my_queue.Client().CreateConsumerGroup(constmap.EventQueueLight, &redisstream.ConsumerOption{})
	consumer.Callback(func(message *redisstream.Payload) error {
		buf, _ := json.Marshal(message.Payload)
		var payload my_queue.Payload
		if err := json.Unmarshal(buf, &payload); err != nil {
			return nil
		}

		my_logger.Infof("get light queue message", zap.String("id", message.ID), zap.Int("event", payload.Event))

		switch payload.Event {
		case constmap.EventScenicUpdate:
			ids, err := utils.ToArray[uint](payload.Payload["ids"].(string), ",")
			if err != nil {
				my_logger.Errorf("scenic update event error", zap.Error(err))
				return nil
			}
			db := models.New()
			poi_biz.EsSyncBiz.SyncScenicByIds(db, ids)

			list := scenics_biz.LoadScenes(db, ids, false)
			for _, scenic := range list {
				_ = scenics_biz.QueryHotelsByPoi(db, constmap.SearchRadius, scenic, func(hotelId uint) (bool, error) {
					hotel_asm.Add2Update([]uint{hotelId})
					return false, nil
				})
			}

			return nil
		case constmap.EventTuanUpdate:
			ids, err := utils.ToArray[uint](payload.Payload["ids"].(string), ",")
			if err != nil {
				my_logger.Errorf("tuan update event error", zap.Error(err))
				return nil
			}
			tuan_biz.EsSyncBiz.SyncTuanByIds(models.New(), ids)
		case constmap.EventOrderPay:
			ids, err := utils.ToArray[uint](payload.Payload["ids"].(string), ",")
			if err != nil {
				my_logger.Errorf("tuan update event error", zap.Error(err))
				return nil
			}
			db := models.New()
			slice.ForEach(ids, func(_ int, orderId uint) {
				if err := orders.OrderPayCallback(db, orderId); err != nil {
					my_logger.Errorf("OrderPayCallback error", zap.Error(err))
				}
			})
		case constmap.EventMail:
			subject := convertor.ToString(payload.Payload["subject"])
			content := convertor.ToString(payload.Payload["body"])

			if err := qiyeweixin_biz.SendQiyeWeixinMessage(subject, content); err != nil {
				my_logger.Errorf("send qiye weixin error", zap.Error(err), zap.String("subject", subject))
			}
		case constmap.EventTaskAction:
			req := task_asm.DoActivityTaskReq{}
			if err := json.Unmarshal([]byte(convertor.ToString(payload.Payload["task_event_req"])), &req); err != nil {
				my_logger.Errorf("[EventTaskAction]", zap.Error(err))
			} else if err := task_asm.ActionEvent(models.New(), req); err != nil {
				my_logger.Errorf("[EventTaskAction]taskBiz", zap.Any("req", req), zap.Error(err))
			}
		case constmap.EventSubMsg:
			if err := wechat_biz.SendSubscribeMsg(models.New(), payload.Payload); err != nil {
				my_logger.Errorf("[EventSubMsg]", zap.Error(err))
			}
		case constmap.EventAirCodeReward:
			code := convertor.ToString(payload.Payload["code"])
			userId, err := convertor.ToInt(payload.Payload["user_id"])
			if code == "" || err != nil {
				my_logger.Errorf("[EventAirCodeReward]", zap.String("code", code), zap.Error(err))
			}
			if err := task_asm.RewardAirCode(models.New(), code, uint(userId)); err != nil {
				my_logger.Errorf("[EventAirCodeReward]", zap.Error(err))
			}
		case constmap.EventWishSyncEs:
			if v, ok := payload.Payload["ids"]; !ok {
				//nothing
			} else if sv, ok1 := v.(string); ok1 {
				ids, _ := utils.ToArray[uint](sv, ",")
				if err := wish_asm.Sync2EsByIds(models.New(), ids); err != nil {
					my_logger.Errorf("[EventWishSyncEs]", zap.Uints("ids", ids), zap.Error(err))
				} else {
					my_logger.Infof("[EventWishSyncEs]", zap.Uints("ids", ids))
				}
			}
		case constmap.EventWishJoinMember:
			//if v, ok := payload.Payload["wish_id"]; ok {
			//
			//}
		case constmap.EventWishSuccess:
			if v, ok := payload.Payload["id"]; !ok {
				my_logger.Errorf("[EventWishSuccess]payload empty")
			} else if id, err := convertor.ToInt(v); err != nil {
				my_logger.Errorf("[EventWishSuccess]payload convert error", zap.Any("id", v), zap.Error(err))
			} else if err := wish_asm.WishSuccess(models.New(), uint(id)); err != nil {
				my_logger.Errorf("[EventWishSuccess]", zap.Any("id", id), zap.Error(err))
			} else {
				my_logger.Infof("[EventWishSuccess]", zap.Any("id", id))
			}
		case constmap.EventWishClose:
			//if v, ok := payload.Payload["id"]; ok {
			//	id, _ := convertor.ToInt(v)
			//	wishId := uint(id)
			//
			//	// TODO 发送关闭心愿单消息
			//
			//
			//}
		}
		return nil
	})

	_ = consumer.Listen()
}

func pdf() {
	consumer := my_queue.Client().CreateConsumerGroup(constmap.EventQueuePdf, &redisstream.ConsumerOption{
		ConsumerCount: 1,
	})
	consumer.Callback(func(message *redisstream.Payload) error {
		buf, _ := json.Marshal(message.Payload)
		var payload my_queue.Payload
		if err := json.Unmarshal(buf, &payload); err != nil {
			return nil
		}

		my_logger.Infof("get queue message", zap.String("id", message.ID), zap.Int("event", payload.Event))

		switch payload.Event {
		case constmap.EventPlanPdf:
			id, _ := convertor.ToInt(payload.Payload["plan_id"])
			doChromeDp(uint(id))
		}
		return nil
	})

	_ = consumer.Listen()
}

// 延迟队列
func delay() {
	//consumer := my_queue.Client().CreateConsumerGroup(constmap.EventQueueDelay, &redisstream.ConsumerOption{IsDelay: true})
	//consumer.Callback(func(message *redisstream.Payload) error {
	//	buf, _ := json.Marshal(message.Payload)
	//	var payload my_queue.Payload
	//	if err := json.Unmarshal(buf, &payload); err != nil {
	//		return nil
	//	}
	//
	//	my_logger.Infof("get queue message", zap.String("id", message.ID), zap.Int("event", payload.Event))
	//
	//	switch payload.Event {
	//	case constmap.EventPayed:
	//		orderId, _ := convertor.ToInt(payload.Payload["order_id"])
	//		return event.OrderPayed(uint(orderId))
	//	}
	//
	//	return nil
	//})
	//
	//_ = consumer.Listen()
}
