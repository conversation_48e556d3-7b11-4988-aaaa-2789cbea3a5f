package cron

import (
	"os"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/business/tuan_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/models"
	"time"

	"github.com/duke-git/lancet/v2/datetime"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

var c *cron.Cron

func Start() {
	c = cron.New(cron.WithChain(cron.DelayIfStillRunning(cron.DefaultLogger)))

	_, _ = c.AddFunc("@daily", clearSession)
	_, _ = c.AddFunc("@hourly", clearUploadFile)
	_, _ = c.AddFunc("@hourly", syncEs)
	_, _ = c.AddFunc("@hourly", syncOta)
	_, _ = c.AddFunc("*/2 * * * *", applySuborderRefund)
	_, _ = c.AddFunc("*/3 * * * *", suborderRefundCheck)
	_, _ = c.AddFunc("*/2 * * * *", applyRefund)
	_, _ = c.AddFunc("*/3 * * * *", refundCheck)
	_, _ = c.AddFunc("@daily", autoVerifyOrder)
	_, _ = c.AddFunc("15 0 * * *", vipExpire)
	_, _ = c.AddFunc("15 0 * * *", userPackageExpire)
	//_, _ = c.AddFunc("3 6,17 * * *", incrHotel)
	_, _ = c.AddFunc("*/2 * * * *", orderDetailProcessing)
	_, _ = c.AddFunc("20 0 * * *", clearPlanHistory)
	_, _ = c.AddFunc("@daily", exportTaskOrders)
	_, _ = c.AddFunc("0 2 1 */1 *", spiderPlan)          //推荐行程
	_, _ = c.AddFunc("0 2 1 */1 *", spiderRecPrompts)    //推荐语
	_, _ = c.AddFunc("0 2 1 */1 *", hotZones)            //热门城市
	_, _ = c.AddFunc("0 3 25 */1 *", hotelPriceSync)     //计算下月酒店均价
	_, _ = c.AddFunc("0,30 * * * *", hotelKnowledgeSync) //同步酒店知识库
	_, _ = c.AddFunc("*/5 * * * *", planCost)            //费用明细
	_, _ = c.AddFunc("0 0 * * 1", weekPKSettle)
	_, _ = c.AddFunc("30 0 * * *", closeWishByDeadline) //关闭超时的心愿单

	wifi()
	tuan()

	c.Start()
	startMq()
}

func clearSession() {
	t := datetime.AddDay(time.Now(), -60)
	db := models.New()

	db.Unscoped().Delete(&models.Session{}, "updated_at<=?", t)
	db.Unscoped().Delete(&models.AdminSession{}, "updated_at<?", t)
}

func clearUploadFile() {
	db := models.New()
	t := datetime.AddDay(time.Now(), -1)

	var list []models.UploadTmp
	db.Where("created_at<?", t).Order("id asc").Limit(1000).Find(&list)
	for _, resource := range list {
		if _, err := os.Stat(resource.Url); err == nil {
			if err := os.Remove(resource.Url); err != nil {
				my_logger.Errorf("remove upload file error", zap.Error(err))
			}
		}

		db.Unscoped().Delete(&resource)
	}
}

func syncEs() {
	db := models.New()
	start := datetime.AddHour(time.Now(), -1)
	poi_biz.EsSyncBiz.FetchCityZone(db, start, false)
	poi_biz.EsSyncBiz.FetchScenicSpots(db, start, false)
	poi_biz.EsSyncBiz.FetchHotel(db, start, false)
	tuan_biz.EsSyncBiz.FetchTuan(db, start, false)
}

func syncOta() {
	db := models.New()
	if config.IsProduction() {
		ota_asm.Zwy.SyncScenic(db)
	}
}
