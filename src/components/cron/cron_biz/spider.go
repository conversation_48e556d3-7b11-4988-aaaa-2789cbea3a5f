package cron_biz

import (
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/chromedp_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"strings"
	"time"
)

// 城市热门标签
func SpiderPlanHotTags(db *gorm.DB, doContinue bool, zoneIds []uint) error {
	//  默认爬取长三角地区的城市
	if len(zoneIds) == 0 {
		if ids, err := utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ","); err == nil {
			zoneIds = ids
		}
	}

	ckey := fmt.Sprintf(constmap.RKSpider, "planHotTags", 0)

	if len(zoneIds) == 0 {
		//db.Model(&models.Zone{}).Where(models.Zone{
		//	Level: constmap.ZoneLevelCity,
		//	State: constmap.Enable,
		//}).Pluck("ID", &zoneIds)
		return nil
	}

	for _, zoneId := range zoneIds {
		if doContinue {
			if my_cache.RedisClient().SIsMember(ckey, zoneId).Val() {
				continue
			}
		}
		var zoneName string
		zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, []uint{zoneId}, 0)
		if zone, ok := zoneMap[zoneId]; ok {
			zoneName = zone.Name
			for zone.Parent != nil {
				zoneName = zone.Parent.Name + " " + zoneName
				zone = zone.Parent
			}
		}

		if prompts, err := my_dify.PlanHotTags(context.Background(), db, zoneName, time.Now().Format(constmap.DateFmtLong)); err != nil {
			my_logger.Errorf("PlanHotTags", zap.Error(err))
		} else if err = my_cache.Set(fmt.Sprintf(constmap.RKPlanHotTags, zoneId), utils.NewGenericList(prompts.CityTags), constmap.TimeDur40d); err != nil {
			my_logger.Errorf("PlanHotTags CacheSet", zap.Error(err))
		} else {
			_, _ = my_cache.RedisClient().Pipelined(func(pipeliner redis.Pipeliner) error {
				pipeliner.SAdd(ckey, zoneId)
				pipeliner.Expire(ckey, constmap.TimeDur15d)
				return nil
			})
		}
	}
	return nil
}

// 生成每月推荐行程
func SpiderPlan(db *gorm.DB, doContinue bool, max int, zoneIds []uint) error {
	//  默认爬取长三角地区的城市
	if len(zoneIds) == 0 {
		if ids, err := utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ","); err == nil {
			zoneIds = ids
		}
	}

	ckey := fmt.Sprintf(constmap.RKSpider, "plan", 0)
	aiModel := ai.NewDify()

	if len(zoneIds) == 0 {
		zoneIds = make([]uint, 0)
		db.Model(&models.Zone{}).Where(models.Zone{
			Level: constmap.ZoneLevelCity,
			State: constmap.Enable,
		}).Pluck("ID", &zoneIds)
	}

	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, 0)
	for _, zoneId := range zoneIds {
		// 先生成热门标签，再根据标签生成推荐行程
		if err := SpiderPlanHotTags(db, doContinue, []uint{zoneId}); err != nil {
			return err
		}
		if doContinue {
			if my_cache.RedisClient().SIsMember(ckey, zoneId).Val() {
				continue
			}
		}
		err := func() error {
			ctx, unlocker := context.WithCancel(context.Background())
			defer unlocker()

			tasks := parallel_task.NewPool(20)
			defer tasks.Release()

			var zone *models.Zone
			var zoneName string
			if z, ok := zoneMap[zoneId]; ok {
				zone = z
				zoneName = z.Name
				for z.Parent != nil {
					zoneName = z.Parent.Name + " " + zoneName
					z = z.Parent
				}
			}
			ctagKey := fmt.Sprintf(constmap.RKSpider, "planWithTag", zoneId)
			recv := utils.NewGenericList[string](nil)
			my_cache.Get(fmt.Sprintf(constmap.RKPlanHotTags, zoneId), recv)
			hotTags := *recv
			hotTags = append(hotTags, "") //默认空标签

			for _, hottag := range hotTags {
				if doContinue && my_cache.RedisClient().SIsMember(ctagKey, hottag).Val() {
					continue
				}
				hottag := hottag

				tasks.AddTask(func() error {
					if err := spiderPlanByHotTag(ctx, db, zone, aiModel, max, zoneName, hottag, ctagKey); err != nil {
						my_logger.Errorf("spiderPlanByHotTag", zap.Error(err))
					}
					return nil
				})

			}

			if err := tasks.Wait(); err != nil {
				my_logger.Errorf("tasksWait", zap.Uint("zoneId", zoneId), zap.Error(err))
			}

			_, _ = my_cache.RedisClient().Pipelined(func(pipeliner redis.Pipeliner) error {
				pipeliner.SAdd(ckey, zoneId)
				pipeliner.Expire(ckey, constmap.TimeDur15d)
				return nil
			})
			return nil
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func spiderPlanByHotTag(ctx context.Context, db *gorm.DB, zone *models.Zone, aiModel ai.IAi, max int, zoneName, hottag, ctagKey string) error {

	detailListCKey := fmt.Sprintf(constmap.RLRobotPlan, zone.ID, hottag)
	chatMsgs := make([]*ai.ChatCompletionResponse, 0)
	plans := make([]any, 0)

	var titles []string
	for i := 0; i < max; i += 1 {

		my_logger.Infof("spiderPlan", zap.Int("index", i), zap.String("zoneName", zoneName), zap.String("hottag", hottag))

		prompt := fmt.Sprintf(`生成"%s"的行程，天数2-7天随机`, zoneName)
		if hottag != "" {
			prompt += fmt.Sprintf(`，行程热门标签为:%s`, hottag)
		}
		if len(titles) > 0 {
			prompt += fmt.Sprintf("\n不要和以下标题重复:\n%s", strings.Join(titles, "\n"))
		}
		b, err := my_dify.MakePlan(ctx, db, 1, prompt)
		if err != nil {
			return errors.Wrapf(err, "MakePlan[%d]:%s", i, zoneName)
		}
		if !strings.Contains(b, "【行程选项】") {
			return utils.NewErrorStr(constmap.ErrorParam, "请求失败,未匹配到【行程选项】")
		}
		if !strings.Contains(b, "【行程选项】") {
			continue
		}
		planCnts := b

		options, err := aiModel.ParseOptions(planCnts)
		if err != nil {
			return err
		}
		var (
			reqId   = fmt.Sprintf("%s:%s", aiModel.GetProvider(), utils.UUID())
			chatMsg = &ai.ChatCompletionResponse{
				PlanLockData: beans.PlanLockData{
					IntegralCostState: constmap.Enable,
				},
				RequestId: reqId,
				Content:   planCnts,
				Provider:  aiModel.GetProvider(),
				UserId:    constmap.RobotUserId,
			}
		)
		chatMsg.PromptOptions = options
		chatMsgs = append(chatMsgs, chatMsg)
		journey, err := plan_biz.ParseAIJourney(db, aiModel, chatMsg)
		if err != nil {
			return errors.Wrapf(err, "ai.Parse:%")
		}
		titles = append(titles, journey.Title)
		plan := new(beans.PlanCard)
		plan.AiReqid = reqId
		plan.Subject = journey.Title
		plan.Subtitle = journey.Subtitle
		plan.Tags = append(plan.Tags, fmt.Sprintf("%d天", len(journey.List)))
		plan.Tags = append(plan.Tags, journey.FitFor)
		dayWaymapPoi := make([][]*beans.JTimeline, 0)
		slice.ForEach(journey.List, func(index int, item *beans.Journey) {
			var waypoints []*beans.JTimeline
			slice.ForEach(item.Timeline, func(i2 int, item2 *beans.JTimeline) {
				waypoints = append(waypoints, item2)
			})
			if len(waypoints) == 0 {
				return
			}

			dayWaymapPoi = append(dayWaymapPoi, waypoints)
		})
		planCost := plan_asm.CalcPlanCost(ctx, db, beans.PlanDetailReq{
			AiReqid: plan.AiReqid,
			Lat:     zone.Lat,
			Lng:     zone.Lng,
		}, time.Unix(options.StartDate, 0), dayWaymapPoi, nil)

		plan.CostAvg = utils.CurrencyInt2Float(planCost.Total)
		plans = append(plans, plan)
	}
	_, _ = my_cache.RedisClient().Pipelined(func(pipeliner redis.Pipeliner) error {
		pipeliner.Del(detailListCKey)
		slice.ForEach(chatMsgs, func(index int, item *ai.ChatCompletionResponse) {
			pipeliner.Set(fmt.Sprintf(constmap.RKAiResp, item.RequestId), item, constmap.TimeDur90d)
		})
		pipeliner.SAdd(detailListCKey, plans...)
		pipeliner.Expire(detailListCKey, constmap.TimeDur90d)
		pipeliner.SAdd(ctagKey, hottag)
		pipeliner.Expire(ctagKey, constmap.TimeDur15d)
		return nil
	})
	slice.ForEach(chatMsgs, func(index int, item *ai.ChatCompletionResponse) {
		// 生成预算明细
		_ = my_queue.Weight(constmap.EventPlanUpdated, gin.H{
			"ai_reqid": item.RequestId,
		})
	})
	return nil
}

func SpiderRecPrompt(db *gorm.DB, doContinue bool, zoneIds []uint) error {
	//  默认爬取长三角地区的城市
	if len(zoneIds) == 0 {
		if ids, err := utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ","); err == nil {
			zoneIds = ids
		}
	}

	ckey := fmt.Sprintf(constmap.RKSpider, "recPrompts", 0)

	if len(zoneIds) == 0 {
		db.Model(&models.Zone{}).Where(models.Zone{
			Level: constmap.ZoneLevelCity,
			State: constmap.Enable,
		}).Pluck("ID", &zoneIds)
	}

	for _, zoneId := range zoneIds {
		if doContinue {
			if my_cache.RedisClient().SIsMember(ckey, zoneId).Val() {
				continue
			}
		}
		var zoneName string
		zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, []uint{zoneId}, 0)
		if zone, ok := zoneMap[zoneId]; ok {
			zoneName = zone.Name
			for zone.Parent != nil {
				zoneName = zone.Parent.Name + " " + zoneName
				zone = zone.Parent
			}
		}

		if prompts, err := my_dify.RecPrompt(context.Background(), db, zoneName, 4); err != nil {
			my_logger.Errorf("RecPrompt", zap.Error(err))
		} else if err = my_cache.Set(fmt.Sprintf(constmap.RKRecPrompts, zoneId), utils.NewGenericList(prompts), constmap.TimeDur40d); err != nil {
			my_logger.Errorf("RecPrompt CacheSet", zap.Error(err))
		} else {
			_, _ = my_cache.RedisClient().Pipelined(func(pipeliner redis.Pipeliner) error {
				pipeliner.SAdd(ckey, zoneId)
				pipeliner.Expire(ckey, constmap.TimeDur15d)
				return nil
			})
		}
	}
	return nil
}

// 城市美食、特产、民俗文化
func SpiderZoneSpeciality(db *gorm.DB, doContinue bool, zoneIds []uint) error {

	if len(zoneIds) == 0 {
		db.Model(&models.Zone{}).Where(models.Zone{
			Level: constmap.ZoneLevelCity,
			State: constmap.Enable,
		}).Pluck("ID", &zoneIds)
	}

	ckey := fmt.Sprintf(constmap.RKSpider, "zoneSpeciality", 0)

	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, 0)

	for _, zoneId := range zoneIds {
		if doContinue && my_cache.RedisClient().SIsMember(ckey, zoneId).Val() {
			continue
		}
		var zone = new(models.Zone)
		var zoneName string
		if z, ok := zoneMap[zoneId]; ok {
			zoneName = z.Name
			zone = z
			for z.Parent != nil {
				zoneName = z.Parent.Name + zoneName
				z = z.Parent
			}
		}

		prompt := fmt.Sprintf("城市:%s\n", zoneName)
		res, err := my_dify.ZoneSpeciality(context.Background(), db, prompt)
		if err != nil {
			return errors.Wrapf(err, "zoneSpeciality[%s]", zoneName)
		}
		jsonInput := res.Zone
		jsonInput.ZoneId = zoneId
		jsonInput.ZoneName = zone.Name
		for i, v := range jsonInput.Cultures {
			v.FeeCent = utils.CurrencyFloat2Int(v.Fee)
			keyword := fmt.Sprintf("%s %s", zoneName, v.CultureName)
			if pic, err := chromedp_biz.BingPic(keyword); err == nil && len(pic) > 0 {
				v.CulturePic = pic
			} else {
				my_logger.Errorf("BingPic", zap.String("keyword", keyword), zap.Error(err))
			}
			jsonInput.Cultures[i] = v
		}
		for i, v := range jsonInput.Foods {
			v.FeeCent = utils.CurrencyFloat2Int(v.Fee)
			keyword := fmt.Sprintf("%s %s", zoneName, v.Name)
			if pic, err := chromedp_biz.BingPic(keyword); err == nil && len(pic) > 0 {
				v.Pic = pic
			} else {
				my_logger.Errorf("BingPic", zap.String("keyword", keyword), zap.Error(err))
			}
			jsonInput.Foods[i] = v
		}
		for i, v := range jsonInput.Specialties {
			v.FeeCent = utils.CurrencyFloat2Int(v.Fee)
			keyword := fmt.Sprintf("%s %s", zoneName, v.Name)
			if pic, err := chromedp_biz.BingPic(keyword); err == nil && len(pic) > 0 {
				v.Pic = pic
			} else {
				my_logger.Errorf("BingPic", zap.String("keyword", keyword), zap.Error(err))
			}
			jsonInput.Specialties[i] = v
		}
		jsonInput.FoodsCost.BreakfastCent = utils.CurrencyFloat2Int(jsonInput.FoodsCost.Breakfast)
		jsonInput.FoodsCost.LunchCent = utils.CurrencyFloat2Int(jsonInput.FoodsCost.Lunch)
		jsonInput.FoodsCost.DinnerCent = utils.CurrencyFloat2Int(jsonInput.FoodsCost.Dinner)

		// 入库处理
		d := &models.ZoneSpeciality{
			ZoneId: zoneId,
		}
		if db.Where(d).Take(&d); d.ID > 0 {
			if db.Model(d).Updates(models.ZoneSpeciality{
				Data: utils.Marshaller[beans.ZoneSpecialityData]{
					Data: &jsonInput,
				},
			}).RowsAffected == 0 {
				my_logger.Errorf("update zoneSpeciality fail", zap.String("zone", zone.Name))
			}
		} else if err := db.Create(&models.ZoneSpeciality{
			ZoneId: zoneId,
			Data: utils.Marshaller[beans.ZoneSpecialityData]{
				Data: &jsonInput,
			},
		}).Error; err != nil {
			my_logger.Errorf("create zoneSpeciality fail", zap.String("zone", zone.Name), zap.Error(err))
		} else {
			my_logger.Infof("zoneSpeciality success", zap.String("zone", zone.Name))
		}
		if doContinue {
			_, _ = my_cache.RedisClient().Pipelined(func(pipeliner redis.Pipeliner) error {
				pipeliner.SAdd(ckey, zoneId)
				pipeliner.Expire(ckey, constmap.TimeDur15d)
				return nil
			})
		}
	}
	return nil
}
