import * as util from './index'

export function uniGetProvider(service) {
  return new Promise((resolve, reject) => {
    uni.getProvider({
      service,
      success: resolve,
      fail: reject,
    })
  })
}

export function uniLogin(params) {
  return new Promise((resolve, reject) => {
    uni.login({
      ...params,
      success: resolve,
      fail: reject,
    })
  })
}


export function uniPay(params) {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      ...params,
      success: resolve,
      fail: reject,
    })
  })
}

export function uniGetSystemInfo() {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success(d) {
        resolve(d)
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

export function uniGetMenuButtonBounding() {
  return new Promise((resolve, reject) => {
    // #ifdef MP
    resolve(uni.getMenuButtonBoundingClientRect())
    // #endif
    // #ifndef MP
    resolve({
      left: 0,
      top: 0,
      height: 0,
      width: 0,
    })
    // #endif
  })
}