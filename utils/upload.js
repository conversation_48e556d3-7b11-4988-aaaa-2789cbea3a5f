import {showToast} from "@/utils/index";

export function imageUpload(file) {
  const url = import.meta.env.VITE_API_BASE_URL + '/api/v1/front/upload';
  
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url,
      filePath: file,
      name: 'file',
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code !== 0) {
          showToast(`上传失败: ${data.msg}`)
          reject(data.msg)
          return
        }
        resolve(data.data)
      },
      fail: (err) => {
        showToast(`上传失败: ${err.toString()}`)
      }
    })
  })
}