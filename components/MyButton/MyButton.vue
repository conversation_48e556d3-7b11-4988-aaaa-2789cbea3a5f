<script setup>
import {computed} from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: '' //primary,success, warning
  },
  link: {
    type: Boolean
  },
  icon: {
    type: String,
    default: ''
  },
  disable: false,
  bordered: {
    type: Boolean,
    default: false,
  },
  formType: {
    type: String,
    default: ''
  },
  openType: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'default' //按钮大小，default， small, large, mini
  },
  customClass: {
    type: String,
    default: '' // 自定义样式类
  }
})

const classStyle = computed(() => {
  const val = [props.type, `size-${props.size}`]

  if (props.link) {
    val.push('link')
  }
  if (props.bordered) {
    val.push('bordered')
  }
  if (props.disable) {
    val.push('disable')
  }

  if (props.customClass) {
    val.push(props.customClass)
  }

  return val
})
</script>

<template>
  <button :class="classStyle" :form-type="formType" :open-type="openType" class="my-button">
    <view class="my-button-content">
      <text v-if="icon" :class="icon" class="iconfont"></text>
      <slot></slot>
    </view>
  </button>
</template>

<style lang="scss" scoped>
@import '@/styles/_define.scss';
@import '@/styles/_mix.scss';

.my-button {
  border-radius: 36rpx;
  font-size: $fontsize;
  padding: 24rpx 48rpx;
  line-height: 100%;
  margin: 0;
  border: 2rpx solid $border-color-v2;
  white-space: nowrap;
  background: white;
  color: $black-color;

  &.size-small {
    font-size: 24rpx;
    padding: 16rpx 40rpx;
  }

  &.size-mini {
    font-size: 20rpx;
    padding: 4rpx 22rpx;
  }

  .my-button-content {
    @include center();
    gap: 12rpx;
  }

  &.primary,
  &.success, &.danger {
    color: white;
    border-color: transparent;
  }

  &.danger {
    background: #FF4D4F;
  }

  &.primary {
    background: $plan-color-3;
  }

  &.warning {
    background: #FBD35A;
    color: #022BAB;
    border-color: transparent;
  }

  &.disable {
    background: #C4C4C4;
    color: white;
    border-color: transparent;
  }

  &.success {
    background: #52c41a;
  }

  &.link {
    background: transparent;
    color: $plan-color-3;
    border: none;
    border-radius: 0;
  }

  &.bordered {

    &.primary {
      border-color: $plan-color-3;
      background: #E7F4FF;
      color: $plan-color-3;
    }

    &.success {
      border-color: #52c41a;
      background: #96cb7b;
      color: #52c41a;
    }
  }

  &::after {
    border: 0;
  }

  &:active {
    opacity: 0.8;
  }
}
</style>
