.publish-wish {
  padding: 20px;

  .section {
    margin-bottom: 20px;

    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
      display: block;
    }

    input, textarea {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .budget-options {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      text {
        background: #f0f0f0;
        padding: 5px 10px;
        border-radius: 4px;
      }
    }

    .media-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
      }
    }

    .note {
      font-size: 12px;
      color: #999;
    }
  }

  button {
    margin-top: 10px;
    background: #007aff;
    color: white;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
  }
}