<template>
  <view class="publish-wish">
    <!-- 出发地和目的地 -->
    <view class="section">
      <text class="section-title">出发地和目的地</text>
      <input v-model="formData.departure" placeholder="请输入出发地"/>
      <input v-model="formData.destination" placeholder="请输入目的地"/>
    </view>

    <!-- 预算 -->
    <view class="section">
      <text class="section-title">预算</text>
      <view class="budget-options">
        <radio-group @change="handleBudgetChange">
          <label v-for="option in budgetOptions" :key="option.value">
            <radio :value="option.value"/>
            {{ option.label }}
          </label>
        </radio-group>
        <input v-if="formData.budget === 'custom'" v-model="formData.customBudget" placeholder="请输入自定义预算"/>
      </view>
    </view>

    <!-- 出发日期 -->
    <view class="section">
      <text class="section-title">出发日期</text>
      <view @click="openCalendar('single')">具体日期</view>
      <view @click="openCalendar('range')">选择月份</view>
      <radio-group @change="handleDateOptionChange">
        <label>
          <radio value="待定"/>
          待定
        </label>
      </radio-group>
    </view>

    <!-- 日历组件（弹出层） -->
    <YjCalendar
        v-if="showCalendar"
        ref="calendarRef"
        :holiday-callback="holiday4Calendar"
        :max-range="[new Date()]"
        :mode="calendarMode"
        popup
        @close="closeCalendar"
        @confirm="handleCalendarConfirm"
    />

    <!-- 必做实现 -->
    <view class="section">
      <text class="section-title">必做实现</text>
      <view v-for="(item, index) in formData.todoList" :key="index">
        <input v-model="item.task" placeholder="请输入必做事项"/>
        <button @click="removeTodo(index)">删除</button>
      </view>
      <button @click="addTodo">添加</button>
    </view>

    <!-- 同行人设置 -->
    <view class="section">
      <text class="section-title">同行人设置</text>
      <input v-model="formData.maxCompanions" placeholder="期望最多同行人数"/>
      <textarea v-model="formData.companionNotes" placeholder="补充说明"/>
    </view>

    <!-- 心愿标题和描述 -->
    <view class="section">
      <text class="section-title">心愿标题和描述</text>
      <input v-model="formData.title" placeholder="请输入标题"/>
      <textarea v-model="formData.description" placeholder="请输入描述"/>
    </view>

    <!-- 标签 -->
    <view class="section">
      <text class="section-title">标签</text>
      <input v-model="newTag" placeholder="添加标签" @confirm="addTag"/>
      <view class="tags">
        <text v-for="(tag, index) in formData.tags" :key="index" @click="removeTag(index)">
          {{ tag }}
        </text>
      </view>
    </view>

    <!-- 图片和视频 -->
    <view class="section">
      <text class="section-title">图片和视频</text>
      <button @click="uploadMedia">上传</button>
      <view class="media-list">
        <image v-for="(media, index) in formData.media" :key="index" :src="media" @click="removeMedia(index)"/>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="section">
      <text class="section-title">联系方式</text>
      <input v-model="formData.phone" placeholder="请输入手机号码"/>
      <text class="note">号码仅后台可见</text>
    </view>

    <!-- 公开范围 -->
    <view class="section">
      <text class="section-title">公开范围</text>
      <radio-group @change="handleVisibilityChange">
        <label>
          <radio value="public"/>
          公开
        </label>
        <label>
          <radio value="private"/>
          私密
        </label>
      </radio-group>
    </view>

    <button @click="submit">提交</button>
  </view>
</template>

<script setup>
import {ref} from 'vue';
import YjCalendar from '@/components/YjCalendar/YjCalendar.vue';
import {holiday4Calendar} from "@/utils";
import dayjs from "dayjs";

const formData = ref({
  departure: '',
  destination: '',
  budget: '',
  customBudget: '',
  date: '',
  month: '',
  todoList: [],
  maxCompanions: '',
  companionNotes: '',
  title: '',
  description: '',
  tags: [],
  media: [],
  phone: '',
  visibility: 'public',
});

const showCalendar = ref(false);
const calendarMode = ref('single');
const calendarRef = ref(null);

const openCalendar = (mode) => {
  calendarMode.value = mode;
  showCalendar.value = true;
};

const handleCalendarConfirm = (value) => {
  if (calendarMode.value === 'single') {
    formData.value.date = value;
  } else {
    formData.value.month = value;
  }
  closeCalendar();
};

const closeCalendar = () => {
  showCalendar.value = false;
};

const newTag = ref('');

const budgetOptions = [
  {label: '1000元以下', value: '1000'},
  {label: '1000-3000元', value: '1000-3000'},
  {label: '3000-5000元', value: '3000-5000'},
  {label: '5000元以上', value: '5000+'},
  {label: '自定义', value: 'custom'},
];

const handleBudgetChange = (e) => {
  formData.value.budget = e.detail.value;
};

const handleDateChange = (e) => {
  formData.value.date = e.detail.value;
};

const handleMonthChange = (e) => {
  formData.value.month = e.detail.value;
};

const handleDateOptionChange = (e) => {
  formData.value.date = e.detail.value;
};

const addTodo = () => {
  formData.value.todoList.push({task: ''});
};

const removeTodo = (index) => {
  formData.value.todoList.splice(index, 1);
};

const addTag = () => {
  if (newTag.value.trim()) {
    formData.value.tags.push(newTag.value.trim());
    newTag.value = '';
  }
};

const removeTag = (index) => {
  formData.value.tags.splice(index, 1);
};

const uploadMedia = () => {
  // 模拟上传逻辑
  uni.chooseImage({
    count: 4 - formData.value.media.length,
    success: (res) => {
      formData.value.media = [...formData.value.media, ...res.tempFilePaths];
    },
  });
};

const removeMedia = (index) => {
  formData.value.media.splice(index, 1);
};

const handleVisibilityChange = (e) => {
  formData.value.visibility = e.detail.value;
};

const submit = () => {
  // 提交逻辑
  console.log('提交数据:', formData.value);
  uni.showToast({
    title: '提交成功',
    icon: 'success',
  });
};
</script>

<style lang="scss">
.publish-wish {
  padding: 20px;

  .section {
    margin-bottom: 20px;

    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
      display: block;
    }

    input, textarea {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .budget-options {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      text {
        background: #f0f0f0;
        padding: 5px 10px;
        border-radius: 4px;
      }
    }

    .media-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
      }
    }

    .note {
      font-size: 12px;
      color: #999;
    }
  }

  button {
    margin-top: 10px;
    background: #007aff;
    color: white;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
  }
}
</style>
