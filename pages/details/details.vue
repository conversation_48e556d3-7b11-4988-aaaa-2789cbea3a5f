<template>
  <YjNavBar :custom-style="{
    background: navBg
  }" @not-found="navTo('pages/index/index')"/>
  <view :class="{ 'popup-open': isPopupOpen }" class="container" @tap="globalStore.pageTap">
    <view v-if="curTab === 'map'" class="map-container">
      <map v-if="isLoad" :include-points="markers" :latitude="latitude" :longitude="longitude" :markers="markers"
           :polyline="polyline" enable-3D @markertap="onMarkerTap"/>
      <MyButton custom-class="tupian" icon="icon-tupian" size="small" type="primary" @tap="curTab = 'pics'">风景图
      </MyButton>
    </view>
    <view v-else class="pics-container">
      <swiper autoplay circular @change="onSwiperChange">
        <swiper-item v-for="(item, index) in detail.covers" :key="index">
          <image :src="item" class="pic" mode="aspectFill"/>
        </swiper-item>
      </swiper>
      <view class="pics-footer">
        <MyButton custom-class="tupian" icon="icon-tupian" size="small" type="primary" @tap="curTab = 'map'">路线图
        </MyButton>
        <view>
          <view class="s">
            <view v-for="(item, index) in detail.covers" :key="index" :class="{
              active: index === curSwiperIndex
            }"></view>
          </view>

          <view class="btn-all" @tap="navTo('pages/details/pics', planQueryParams)">查看全部</view>
        </view>

      </view>
    </view>

    <view class="main-data">
      <Info :detail="detail" :show-like-animation="showLikeAnimation" @reload="getDetail(true)"/>
      <view class="notice">
        <view>
          <text class="iconfont icon-wenxintishi"></text>
          出行注意事项
        </view>
        <view class="content">
          <view v-for="(item, index) in detail.notice" :key="index">{{ item }}
          </view>
        </view>
      </view>
      <view class="section-list">
        <view v-for="(section, index) in detail.sections" :key="index" class="section-item">
          <view class="section-item-h">
            <view>
              <text/>
            </view>

            <view class="subject">{{ `${section.section_name}-${section.subject}` }}</view>
          </view>
          <view class="timeline">
            <template v-for="(line, li) in section.timeline" :key="li">
              <view :class="`line-${line.type}`" class="timeline-item" @tap="onPoiDetail(index, line)">
                <view class="left">
                  <text></text>
                </view>
                <view class="right">
                  <text :class="getTimeLineIcon(line)" class="timeline-icon iconfont"></text>
                  <view v-if="['plane', 'train', 'bus'].includes(line.type)" class="transport">
                    <view class="transport-from">
                      <view>
                        <text>{{ line.transport.from.zone_name }}</text>
                        <image :src="getCdnUrl('/static/fly-to.png')" mode="widthFix"/>
                      </view>
                      <text class="station">{{ line.transport.from.station }}</text>
                    </view>
                    <view class="transport-to">
                      <text>{{ line.transport.to.zone_name }}</text>
                      <text class="station">{{ line.transport.to.station }}</text>
                    </view>
                  </view>
                  <template v-else>
                    <image v-if="line.pics.length > 0" :src="line.pics[0]" class="pic" mode="scaleToFill"/>
                    <view class="poi-detail-info">
                      <view class="time-title">{{ line.title }}</view>
                      <view class="time-desc">
                        {{ line.desc }}
                      </view>
                      <view class="tags">
                        <text v-for="tag in line.tags">{{ tag }}</text>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>

    <view class="footer">
      <view>
        <YjTooltip content="收藏行程" placement="top" type="detail-fav">
          <view class="item fav" @tap="onTapFav">
            <text :class="{
              active: detail.is_own,
              'icon-a-shoucang-weishoucang': !detail.is_own,
              'icon-shoucang3': detail.is_own
            }" class="text iconfont fav"/>
            收藏
          </view>

        </YjTooltip>

        <YjTooltip content="分享海报+500积分" placement="top" type="detail-share-poster">
          <template #icon>
            <image src="/static/share_hint.png" style="width: 90rpx; height: 72rpx;"/>
          </template>
          <view class="item" @tap="onShare">
            <text class="iconfont icon-haibao"/>
            分享海报
          </view>
        </YjTooltip>

        <YjTooltip content="一键导出PDF" placement="top" type="detail-export-pdf">
          <view class="item" @tap="openPdfExportPopup">
            <text class="iconfont icon-file-pdf"/>
            下载完整攻略
          </view>
        </YjTooltip>

        <YjTooltip content="编辑行程" placement="top" type="detail-edit">
          <view class="item" @tap="onEdit">
            <text class="text iconfont icon-bg-edit"/>
            编辑行程
          </view>
        </YjTooltip>
      </view>
      <view>
        <MyButton type="primary" @tap="onBuy">一键预订</MyButton>
      </view>

    </view>
  </view>

  <my-activity-popup v-if="activity.id" @do="navTo('pages/activity/activity', { code: activity.uuid })">
    <view :style="{ backgroundImage: `url('${activity.pic}')` }" class="laxin-activity">
      <view class="data">
        <view>
          您已经为
          <image :src="detail.avatar || getCdnUrl('/static/mine/user.png')" class="avatar" mode="widthFix"></image>
          <text class="nickname">{{ detail.nickname }}</text>
        </view>
        <view>助力成功，您也来领积分吧。</view>
      </view>
    </view>
  </my-activity-popup>

  <!-- PDF 导出弹窗 -->
  <uni-popup ref="pdfExportPopup" :mask-click="true" type="center" @maskClick="handlePopupMaskClick">
    <view class="pdf-export-popup" @touchmove.stop.prevent>
      <view class="pdf-export-header">
        <view class="header-decoration">
          <view class="deco-circle deco-1"></view>
          <view class="deco-circle deco-2"></view>
          <view class="deco-triangle"></view>
        </view>
        <view class="pdf-title-main">下载完整行程攻略</view>
        <view class="header-sparkles">
          <view class="sparkle sparkle-1">✨</view>
          <view class="sparkle sparkle-2">⭐</view>
          <view class="sparkle sparkle-3">✨</view>
          <view class="sparkle sparkle-4">💫</view>
          <view class="sparkle sparkle-5">⭐</view>
          <view class="sparkle sparkle-6">✨</view>
          <view class="sparkle sparkle-7">🌟</view>
        </view>
      </view>

      <view class="pdf-export-content" @touchmove.stop>
        <view class="pdf-advantages">
          <view class="advantage-item">
            <view class="advantage-icon-wrapper">
              <text class="iconfont icon-24px"></text>
              <view class="icon-glow"></view>
            </view>
            <view class="advantage-content">
              <view class="advantage-title">离线随时查</view>
              <view class="advantage-desc">打印、转发、编辑、比截图更清晰</view>
            </view>
            <view class="advantage-decoration">
              <view class="deco-dot"></view>
              <view class="deco-line"></view>
            </view>
          </view>
          <view class="advantage-item">
            <view class="advantage-icon-wrapper">
              <text class="iconfont icon-zhuyecansifanxin"></text>
              <view class="icon-glow"></view>
            </view>
            <view class="advantage-content">
              <view class="advantage-title">内容更加详尽</view>
              <view class="advantage-desc">路线+美食+拍照地点+特产一键打包</view>
            </view>
            <view class="advantage-decoration">
              <view class="deco-dot"></view>
              <view class="deco-line"></view>
            </view>
          </view>
        </view>

        <view class="pdf-export-tips">
          <view class="tips-header">
            <view class="tips-title">导出详情</view>
            <view class="tips-decoration">
              <view class="tips-dot"></view>
              <view class="tips-dot"></view>
              <view class="tips-dot"></view>
            </view>
          </view>
          <view class="tip-item cost-item">
            <view class="tip-icon-wrapper">
              <text class="iconfont icon-sijiaoxing-01"></text>
            </view>
            <view class="tip-content">
              <text>仅需消耗：</text>
              <text v-if="globalStore.planPdfCostAmount > 0" class="cost-amount">{{
                  globalStore.planPdfCostAmount
                }}
              </text>
              <text v-else class="cost-amount free">0</text>
              <text> 积分</text>
            </view>
          </view>
          <view class="tip-item time-item">
            <view class="tip-icon-wrapper">
              <text class="iconfont icon-shijian"></text>
            </view>
            <view class="tip-content">
              <text>预计耗时 5 分钟</text>
            </view>
          </view>
          <view class="tip-item info-item">
            <view class="tip-icon-wrapper">
              <text class="iconfont icon-wenxintishi"></text>
            </view>
            <view class="tip-content">
              <text>您可以在行程收藏中查看导出的 PDF 文件</text>
            </view>
          </view>
        </view>
      </view>

      <view class="pdf-export-footer">
        <view class="pdf-btn pdf-btn-cancel" @tap="closePdfExportPopup">取消</view>
        <view class="pdf-btn pdf-btn-primary" @tap="handlePdfExport">下载</view>
      </view>
    </view>
  </uni-popup>
  <!-- 积分不足弹窗 -->
  <MyPointNotEnough v-if="showPointNotEnough" @close="handlePointNotEnoughClose"/>

</template>

<script setup>
import {routeV2} from '../../api'
import {decodeQuery, getParamsFromUrl, navTo, showToast, getCdnUrl, getH5Url} from '../../utils'
import {getRainbowColors} from '@/utils/colors'
import {planRemove} from '@/api/user'
import {onLoad, onPageScroll, onShow} from '@dcloudio/uni-app'
import {computed, nextTick, ref} from 'vue'
import {planDetail, planPoster, planSubmit, planPdf} from '../../api/plan'
import {useGlobalStore} from '../../store/global'
import YjTooltip from '@/components/YjTooltip/YjTooltip.vue'
import MyActivityPopup from "@/components/MyActivityPopup/MyActivityPopup.vue";
import {trackEvent} from "@/utils/tracker";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import MyButton from "@/components/MyButton/MyButton.vue";
import Info from "@/pages/details/components/Info.vue";
import MyPointNotEnough from "@/components/MyPointNotEnough/MyPointNotEnough.vue";

const pageScroll = ref(0)
const showPointNotEnough = ref(false) // 是否显示积分不足弹窗
const pendingPdfExport = ref(false) // 是否有待处理的PDF导出
const lastPdfCostAmount = ref(0) // 上次导出所需积分数
const detail = ref({
  plan_id: '',
  sections: [],
  prompt_options: {},
  notice: [],
  from: '',
  to: '',
  ai_reqid: '',
  activities: [],
  covers: [],
  is_own: false,
})
const curSwiperIndex = ref(0)
const curTab = ref('map')
const availableTimelineType = ['hotel', 'scene']
const globalStore = useGlobalStore()
const polyline = ref([])
const latitude = ref('')
const longitude = ref('')
const isLoad = ref(false)
const isInit = ref(false)
const pdfExportPopup = ref(null)
const isPopupOpen = ref(false) // 弹窗是否打开
const showLikeAnimation = ref(false)
const markers = computed(() => {
  let markerIndex = 10
  const ret = []
  if (longitude.value.length && latitude.value.length) {
    ret.push({
      id: markerIndex,
      longitude: longitude.value,
      latitude: latitude.value,
      title: '出发地',
      ...getMarkerIcon('hotel')
    })
  }

  for (const section of detail.value.sections) {
    for (const timelineElement of section.timeline) {
      if (!availableTimelineType.includes(timelineElement.type)) {
        continue
      }
      const it = {
        id: ++markerIndex,
        longitude: timelineElement.poi.lng,
        latitude: timelineElement.poi.lat,
        title: timelineElement.poi.name,
        ...getMarkerIcon(timelineElement.type)
      }
      ret.push(it)
    }
  }

  return ret
})
const query = ref({})
const activity = computed(() => detail.value.activities.length > 0 ? detail.value.activities[0] : {})

const navBg = computed(() => {
  const max = 30
  let st = pageScroll.value
  if (st >= max) {
    st = max
  }
  return `rgba(255,255,255, ${st / max})`
})

function onSwiperChange({detail: {current}}) {
  curSwiperIndex.value = current
}

async function onShare() {
  if (!detail.value.is_own) {
    const {data} = await planSubmit(planQueryParams.value)
    detail.value.plan_id = data.id
    detail.value.is_own = true
  }

  const url = getH5Url(`#/plan/view_poster?plan_id=${detail.value.plan_id}`)

  navTo(url)

}

function handlePointNotEnoughClose() {
  console.log('关闭积分不足弹窗')
  // 关闭积分不足弹窗
  showPointNotEnough.value = false
}

function checkAndRetryPdfExport() {
  // 检查用户积分是否足够
  if (globalStore.userInfo?.package_info?.amount >= lastPdfCostAmount.value) {
    console.log('积分充足，自动重试PDF导出')
    // 清除标记
    pendingPdfExport.value = false
    lastPdfCostAmount.value = 0

    // 自动重新触发PDF导出
    handlePdfExport()
  } else {
    console.log('积分仍不足，等待用户手动操作')
  }
}

function getTimeLineIcon(line) {
  switch (line.type) {
    case 'hotel':
      return 'hotel icon-chuangtouka'
    case 'scene':
      return 'scene icon-fengjingqu3'
    case 'bus':
      return 'bus icon-daba'
    case 'train':
      return 'train icon-x_gaotie'
    case 'plane':
      return 'plane icon-feiji1'
    default:
      return 'disable icon-jiaoyin'
  }
}

onLoad((q) => {
  if (q.q) {
    q = getParamsFromUrl(decodeURIComponent(q.q));
  }
  if (q.showLikeAnimation === 'true') {
    showLikeAnimation.value = true
  }
  Object.assign(query.value, decodeQuery(q))
})

onShow(() => {
  getDetail()

  // 检查是否需要自动重试PDF导出
  if (pendingPdfExport.value) {
    // 延时检查，确保页面和数据完全加载
    setTimeout(() => {
      checkAndRetryPdfExport()
    }, 500)
  }
})

const planQueryParams = computed(() => {
  const p = {}

  if (detail.value.plan_id) {
    p.plan_id = detail.value.plan_id
  } else if (query.value.plan_id) {
    p.plan_id = query.value.plan_id
  } else if (query.value.ai_reqid) {
    p.ai_reqid = query.value.ai_reqid
  }

  return p
})

function getDetail(force = false) {
  if (isInit.value && !force) {
    return
  }
  planDetail(planQueryParams.value).then((res) => {
    const {data} = res

    data.notice = data.notice.split('\n')

    Object.assign(detail.value, data)

    getRoute()
  }).finally(() => isInit.value = true)
}

async function onBuy() {
  if (!detail.value.is_own) {
    await onTapFav(false)
  }

  const params = {
    plan_id: detail.value.plan_id,
    type: 'package'
  }

  navTo('pages/orderpreview/orderpreview', params)
}

function getMarkerIcon(type = 'scene') {
  return {
    iconPath: getCdnUrl(`/static/plan/marker-${type}.png`),
    width: 48,
    height: 48
  }
}

async function onEdit() {
  if (!detail.value.is_own) {
    await onTapFav(false)
  }
  navTo('pages/edittraveldetails/edittraveldetails', {plan_id: detail.value.plan_id}).then(() => {
    isInit.value = false
  })
}

function onPoiDetail(index, poi) {
  if (poi.type === 'scene') {
    navTo('pages/scenic/scenic', {id: poi.item_id})
  } else if (poi.type === 'hotel') {
    navTo('pages/hotel/hotel', {id: poi.item_id})
  }
}

onPageScroll(({scrollTop}) => {
  pageScroll.value = scrollTop
})

async function share() {
  const {data: {url}} = await planPoster(detail.value.plan_id)

  const data = {
    path: '/pages/details/details?plan_id=' + detail.value.plan_id,
    title: detail.value.subject,
    imageUrl: url
  }

  return data
}

async function onTapFav(toast = true) {
  if (detail.value.is_own) {
    await planRemove(detail.value.plan_id)
    detail.value.is_own = false
  } else {
    const {data} = await planSubmit(planQueryParams.value)
    detail.value.is_own = true
    detail.value.plan_id = data.id

    trackEvent('click_fav_plan')
  }

  if (toast) {
    showToast(detail.value.is_own ? '收藏成功' : '取消成功')
  }
}

function onMarkerTap(e) {

}

function getRoute() {
  globalStore.getLocation().then(async (res) => {
    const {lng, lat} = res
    longitude.value = lng
    latitude.value = lat

    const params = {
      lng,
      lat,
      ...planQueryParams.value
    }
    params.ai_reqid = detail.value.ai_reqid

    const {data: {routes}} = await routeV2(params, false)
    if (routes.length === 0) {
      return
    }
    polyline.value = routes.map((dayPoints, index) => {
      const points = dayPoints.polyline.map(item => {
        return {
          longitude: item[0],
          latitude: item[1]
        }
      })
      return {
        points,
        width: 6,
        arrowLine: true,
        color: getRainbowColors(index)
      }
    })

    isLoad.value = true
  })
}

function openPdfExportPopup() {
  // 检查PDF生成状态
  if (detail.value.pdf_state === 1) {
    showToast('PDF正在生成中，请稍后在行程收藏中查看')
    return
  }

  if (detail.value.pdf_state === 2 && detail.value.pdf_url) {
    showToast('PDF已生成，请在行程收藏中查看')
    return
  }

  isPopupOpen.value = true
  pdfExportPopup.value.open()
}

function closePdfExportPopup() {
  isPopupOpen.value = false
  pdfExportPopup.value.close()
}

function handlePopupMaskClick() {
  isPopupOpen.value = false
}

async function handlePdfExport() {
  try {
    // 确保行程已收藏
    if (!detail.value.is_own) {
      const {data} = await planSubmit(planQueryParams.value)
      detail.value.is_own = true
      detail.value.plan_id = data.id
    }

    // 调用导出PDF的API
    const {data: {tpl_msg_id}} = await planPdf(detail.value.plan_id)

    //#ifdef MP
    if (tpl_msg_id) {
      await new Promise(resolve => {
        uni.requestSubscribeMessage({
          tmplIds: [tpl_msg_id],
          fail(e) {
            console.log('=========requestSubscribeMessage error', e)
          },
          complete: resolve,
        })
      })
    }
    //#endif

    closePdfExportPopup()
    showToast('PDF 导出任务已提交，导出过程需要 5 分钟左右，请在行程收藏中查看进度')


    // 清除PDF导出重试标记（成功导出）
    pendingPdfExport.value = false
    lastPdfCostAmount.value = 0

    // 跟踪导出事件
    trackEvent('click_export_pdf')

    // 延时跳转到我的行程规划页面
    setTimeout(() => {
      navTo('pages/mine/plans/plans')
    }, 1500) // 1.5秒后跳转，确保用户看到成功提示
  } catch (error) {
    if (error.code == 900021) {
      // 设置PDF导出重试标记
      pendingPdfExport.value = true
      lastPdfCostAmount.value = globalStore.planPdfCostAmount

      // 先重置状态，确保能够触发响应式更新
      showPointNotEnough.value = false

      // 使用 nextTick 确保在下一个 DOM 更新周期设置为 true
      nextTick(() => {
        // 弹出积分不足弹窗
        showPointNotEnough.value = true
      })
    } else if (error.msg) {
      showToast(error.msg)
    } else {
      showToast('导出失败，请稍后重试')
    }
    console.error('PDF导出失败:', error)
  }
}
</script>

<style lang="scss" scoped>
@import 'details.scss';
</style>
